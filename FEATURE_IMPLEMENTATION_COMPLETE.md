# Artifact AI 菜单功能实现完成

## 功能概述

已成功实现在 Artifact 组件的 Edit 按钮左边添加 AI 菜单功能，支持"修复有问题代码"和"继续优化"两个操作。

## 实现的功能

### 1. AI 菜单按钮
- 在 Artifact 组件的工具栏中，Edit 按钮左侧添加了 AI 助手按钮（Magic 图标）
- 按钮仅在 `inDoc` 为 true 或 `onAIAction` 属性存在时显示
- 点击按钮显示下拉菜单

### 2. 下拉菜单选项
- **修复有问题代码**: 直接调用 AI 助手进行代码修复
- **继续优化**: 允许用户输入优化要求描述，然后调用 AI 助手

### 3. 双重实现逻辑

#### AINode 上下文
- 使用 AINode 中的 AI 助手实现逻辑
- 通过 `aiItemClicked` 函数调用 AI 助手
- 支持 `fix_codes_bug` 和 `improve_codes` 两种 action
- 监听 AI 响应完成，自动更新 artifact 内容

#### inDoc 上下文  
- 使用 AIModal 进行 AI 操作
- 通过 dispatch `AI_ASSISTANT_DIALOG` action 唤起 AIModal
- 传递 artifact 内容和用户输入给 AIModal

### 4. 接受/拒绝功能
- AI 助手完成请求后，在 artifact 顶端显示接受/拒绝按钮
- **接受**: 保留 AI 生成的新代码
- **拒绝**: 恢复到原有代码
- 按钮使用绿色（接受）和红色（拒绝）图标

## 技术实现细节

### 1. Artifact 组件修改
```javascript
// 添加 AI 菜单按钮
{
    (inDoc || onAIAction) &&
    <Tooltip title={intl.formatMessage({ id: 'ai_assistant' })}>
        <div onClick={handleAIMenuClick}>
            <Magic size={18} />
        </div>
    </Tooltip>
}

// AI 菜单 Popover
<Popover open={aiMenuOpen} anchorEl={aiMenuAnchor}>
    <div onClick={() => handleAIAction('fix_codes_bug')}>
        修复有问题代码
    </div>
    <div onClick={() => handleAIAction('improve_codes', userInput)}>
        继续优化
    </div>
</Popover>
```

### 2. AINode 中的处理逻辑
```javascript
// AI 操作处理
const handleArtifactAIAction = React.useCallback((action, artifact, userInput = '') => {
    // 保存原始内容
    setOriginalArtifactContent(artifact.content);
    setProcessingArtifactId(artifact.id);
    
    // 构建 AI 请求
    const aiArgs = [
        { name: 'code_content', value: aiContent }
    ];
    
    if (action === 'improve_codes' && userInput) {
        aiArgs.push({ name: 'user_requirements', value: userInput });
    }
    
    // 调用 AI 助手
    aiItemClicked(action, aiArgs, '', '');
}, [aiItemClicked]);

// 监听 AI 响应完成
React.useEffect(() => {
    if (data.aigc_done && aiResponse?.content && processingArtifactId !== null) {
        if (data.ai_action === 'fix_codes_bug' || data.ai_action === 'improve_codes') {
            handleAIResponseComplete(aiResponse.content);
        }
    }
}, [data.aigc_done, aiResponse?.content, processingArtifactId, data.ai_action]);
```

### 3. inDoc 上下文处理
```javascript
// 在 Artifact 组件中
const handleAIAction = (action, userInput = '') => {
    if (inDoc) {
        // 构建 AI 内容
        let aiContent = selectedArtifact.content;
        if (selectedArtifact.type === 'Mermaid') {
            aiContent = `\`\`\`mermaid\n${selectedArtifact.content}\n\`\`\``;
        }
        
        // 唤起 AIModal
        dispatch({
            type: AI_ASSISTANT_DIALOG,
            value: {
                caller: 'artifact',
                visible: true,
                action: action,
                selectedText: aiContent,
                userInput: userInput
            }
        });
    }
};
```

### 4. 接受/拒绝 UI
```javascript
// 接受/拒绝按钮
{showAcceptReject && (
    <div style={{ position: 'absolute', top: -35, left: '50%' }}>
        <Tooltip title="拒绝">
            <div onClick={onReject}>
                <X size={16} color="#d32f2f" />
            </div>
        </Tooltip>
        <Tooltip title="接受">
            <div onClick={onAccept}>
                <Check size={16} color="#2e7d32" />
            </div>
        </Tooltip>
    </div>
)}
```

## 国际化支持

添加了以下翻译键：

### 英文 (en.js)
```javascript
"ai_assistant": "AI Assistant",
"fix_codes_bug": "Fix Code Issues", 
"improve_codes": "Continue Optimization",
"improve_codes_prompt": "Please describe your optimization requirements:",
"accept": "Accept",
"reject": "Reject"
```

### 中文 (cn.js)
```javascript
"ai_assistant": "AI 助手",
"fix_codes_bug": "修复有问题代码",
"improve_codes": "继续优化", 
"improve_codes_prompt": "请描述您的优化要求：",
"accept": "接受",
"reject": "拒绝"
```

## 使用流程

### 在 AINode 中使用
1. 生成包含 Mermaid 或 SVG 的 AI 内容
2. 点击 "View Mermaid/SVG Content" 按钮显示 Artifact
3. 鼠标悬停显示工具栏，点击 AI 助手按钮（Magic 图标）
4. 选择"修复有问题代码"或"继续优化"
5. 如果选择"继续优化"，输入优化要求
6. AI 处理完成后，在 artifact 顶部显示接受/拒绝按钮
7. 选择接受保留新代码，或拒绝恢复原代码

### 在 inDoc 中使用
1. 在文档编辑器中创建 mermaid 或 svg 代码块
2. 代码块自动显示为 Artifact 组件
3. 鼠标悬停显示工具栏，点击 AI 助手按钮
4. 选择相应的 AI 操作
5. AIModal 弹出，处理 AI 请求
6. 在 AIModal 中查看和处理 AI 响应

## 文件修改清单

1. **src/components/flow/Artifact.js**
   - 添加 AI 菜单按钮和 Popover
   - 添加接受/拒绝按钮 UI
   - 实现双重上下文处理逻辑

2. **src/components/flow/AINode.js**
   - 添加 AI 操作相关状态管理
   - 实现 AI 请求处理和响应监听
   - 添加接受/拒绝处理逻辑

3. **src/components/plate-ui/code-block-element.tsx**
   - 为 inDoc 上下文中的 Artifact 添加必要的 props

4. **src/locales/en.js** 和 **src/locales/cn.js**
   - 添加相关翻译键

## 功能特点

- ✅ 支持 Mermaid 和 SVG 代码的 AI 处理
- ✅ 双重上下文支持（AINode 和 inDoc）
- ✅ 用户友好的接受/拒绝机制
- ✅ 完整的国际化支持
- ✅ 智能内容提取和更新
- ✅ 原始内容备份和恢复
- ✅ 响应式 UI 设计

这个实现完全满足了原始需求，提供了完整的 Artifact AI 菜单功能，包括代码修复、优化和用户确认机制。
