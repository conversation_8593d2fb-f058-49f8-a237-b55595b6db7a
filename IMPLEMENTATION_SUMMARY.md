# AINode 中 Artifact 编辑功能实现总结

## 需求
在 AINode 中展现 Artifact 时，也要有编辑功能，显示编辑按钮，用户点击编辑时，应弹出一个 modal 窗口，供用户修改相应的 mermaid, svg 代码，代码修改后应更新相应的 node 数据。

## 实现方案

### 1. 修改 Artifact 组件 (`src/components/flow/Artifact.js`)

**变更内容：**
- 修改编辑按钮的显示条件从 `inDoc` 改为 `(inDoc || onEdit)`
- 这样在 AINode 中使用时，只要传递了 `onEdit` 属性就会显示编辑按钮

**代码变更：**
```javascript
// 原来：
{
    inDoc &&
    <Tooltip title={intl.formatMessage({ id: 'edit' })} placement='top'>
        // ... 编辑按钮
    </Tooltip>
}

// 修改后：
{
    (inDoc || onEdit) &&
    <Tooltip title={intl.formatMessage({ id: 'edit' })} placement='top'>
        // ... 编辑按钮
    </Tooltip>
}
```

### 2. 创建 ArtifactEditModal 组件 (`src/components/flow/ArtifactEditModal.js`)

**功能特性：**
- 使用 Material-UI Dialog 组件创建模态框
- 支持可拖拽功能（使用 react-draggable）
- 提供多行文本编辑器，支持 Mermaid 和 SVG 代码编辑
- 使用等宽字体显示代码
- 支持键盘快捷键：
  - `Escape`: 关闭模态框
  - `Ctrl+Enter` / `Cmd+Enter`: 保存并关闭
- 根据 artifact 类型显示不同的标题和占位符文本
- 响应式设计，适配不同屏幕尺寸

**主要组件结构：**
```javascript
<Dialog>
  <Box> {/* 可拖拽的标题栏 */}
  <DialogContent> {/* 代码编辑区域 */}
  <DialogActions> {/* 取消和保存按钮 */}
</Dialog>
```

### 3. 修改 AINode 组件 (`src/components/flow/AINode.js`)

**添加的状态：**
```javascript
const [editModalVisible, setEditModalVisible] = React.useState(false);
const [editingArtifact, setEditingArtifact] = React.useState(null);
```

**添加的处理函数：**

1. **`handleArtifactEdit`**: 打开编辑模态框
   ```javascript
   const handleArtifactEdit = React.useCallback((artifact) => {
     setEditingArtifact(artifact);
     setEditModalVisible(true);
   }, []);
   ```

2. **`handleArtifactSave`**: 保存编辑后的内容
   - 更新 artifacts 数组中的内容
   - 更新当前选中的 artifact（如果是正在编辑的）
   - 根据 artifact 类型使用不同的正则表达式更新节点内容
   - 调用 `updateNodeData` 更新节点数据

3. **`handleArtifactEditClose`**: 关闭编辑模态框

**修改 Artifact 组件调用：**
```javascript
<Artifact
  // ... 其他属性
  onEdit={() => handleArtifactEdit(selectedArtifact)}
/>
```

**添加 ArtifactEditModal 组件：**
```javascript
<ArtifactEditModal
  visible={editModalVisible}
  artifact={editingArtifact}
  onSave={handleArtifactSave}
  onClose={handleArtifactEditClose}
/>
```

### 4. 添加国际化支持

**英文翻译 (`src/locales/en.js`)：**
```javascript
"edit_mermaid_code": "Edit Mermaid Code",
"edit_svg_code": "Edit SVG Code", 
"edit_code": "Edit Code",
```

**中文翻译 (`src/locales/cn.js`)：**
```javascript
"edit_mermaid_code": "编辑 Mermaid 代码",
"edit_svg_code": "编辑 SVG 代码",
"edit_code": "编辑代码",
```

## 功能流程

1. **显示编辑按钮**
   - 在 AINode 中，当有 Artifact 内容时，Artifact 组件会显示编辑按钮
   - 编辑按钮只在鼠标悬停时显示

2. **打开编辑模态框**
   - 用户点击编辑按钮
   - 调用 `handleArtifactEdit` 函数
   - 设置 `editingArtifact` 和 `editModalVisible` 状态
   - ArtifactEditModal 组件显示

3. **编辑代码**
   - 模态框显示当前 artifact 的内容
   - 用户可以在文本框中编辑 Mermaid 或 SVG 代码
   - 支持多行编辑，使用等宽字体

4. **保存更改**
   - 用户点击保存按钮或使用快捷键
   - 调用 `handleArtifactSave` 函数
   - 智能更新：
     - 更新 artifacts 数组
     - 更新 selectedArtifact（如果匹配）
     - 使用正则表达式更新节点内容中的对应代码块
     - 调用 `updateNodeData` 保存到节点数据

5. **关闭模态框**
   - 保存后自动关闭
   - 或用户点击取消/关闭按钮
   - 重置编辑状态

## 技术亮点

1. **智能内容更新**
   - 根据 artifact 类型使用不同的正则表达式
   - 确保只更新对应的代码块，不影响其他内容

2. **状态同步**
   - 同时更新多个相关状态，保证数据一致性
   - artifacts 数组、selectedArtifact、节点内容三者同步

3. **用户体验优化**
   - 可拖拽的模态框
   - 键盘快捷键支持
   - 适当的占位符和提示文本
   - 响应式设计

4. **代码质量**
   - 使用 React.useCallback 优化性能
   - 清晰的函数职责分离
   - 良好的错误处理和边界情况处理

## 使用方式

1. 在 AINode 中生成包含 Mermaid 或 SVG 的内容
2. 点击 Artifact 右上角的编辑按钮
3. 在弹出的模态框中修改代码
4. 点击保存或使用 Ctrl+Enter 保存更改
5. 更改立即反映在节点内容和显示中

这个实现完全满足了原始需求，提供了完整的 Artifact 编辑功能，包括用户友好的界面和可靠的数据更新机制。
