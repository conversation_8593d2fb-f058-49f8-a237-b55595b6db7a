import React, { useState, useEffect, useRef } from 'react';
import { useIntl } from 'react-intl';
import { 
  Dialog, 
  DialogContent, 
  DialogActions, 
  Button, 
  TextField,
  Box,
  Typography
} from '@mui/material';
import { Close } from '@styled-icons/material';
import Draggable from 'react-draggable';
import { useMediaQuery } from 'react-responsive';
import { MOBILE_MEDIA_QUERY } from '../../utils/constants';

// 创建可拖拽的对话框组件
function PaperComponent(props) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <div {...props} />
    </Draggable>
  );
}

const ArtifactEditModal = ({ 
  visible, 
  artifact, 
  onSave, 
  onClose 
}) => {
  const intl = useIntl();
  const isMobile = useMediaQuery({ query: MOBILE_MEDIA_QUERY });
  const [content, setContent] = useState('');
  const textInputRef = useRef(null);

  useEffect(() => {
    if (visible && artifact) {
      setContent(artifact.content || '');
      // 延迟聚焦，确保模态框已完全渲染
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.focus();
        }
      }, 100);
    }
  }, [visible, artifact]);

  const handleSave = () => {
    if (onSave) {
      onSave(content);
    }
    onClose();
  };

  const handleClose = () => {
    setContent('');
    onClose();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      handleClose();
    } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSave();
    }
  };

  if (!visible || !artifact) {
    return null;
  }

  const getTitle = () => {
    switch (artifact.type) {
      case 'Mermaid':
        return intl.formatMessage({ id: 'edit_mermaid_code' }, { defaultMessage: 'Edit Mermaid Code' });
      case 'SVG':
        return intl.formatMessage({ id: 'edit_svg_code' }, { defaultMessage: 'Edit SVG Code' });
      default:
        return intl.formatMessage({ id: 'edit_code' }, { defaultMessage: 'Edit Code' });
    }
  };

  return (
    <Dialog
      open={visible}
      PaperComponent={PaperComponent}
      aria-labelledby="draggable-dialog-title"
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          position: 'fixed',
          left: '50%',
          top: '20%',
          transform: 'translate(-50%, -50%)',
          bgcolor: 'white',
          borderRadius: '8px',
          boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)',
          border: '1px solid #e0e0e0',
          zIndex: 1300,
          width: '80vw',
          maxWidth: '800px',
          height: '70vh',
          maxHeight: '600px',
          m: 0,
          display: 'flex',
          flexDirection: 'column'
        },
      }}
      onKeyDown={handleKeyDown}
    >
      {/* 标题栏 */}
      <Box
        id="draggable-dialog-title"
        className="handle"
        sx={{
          cursor: 'move',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
          borderBottom: '1px solid #e0e0e0',
          bgcolor: '#f5f5f5',
          borderRadius: '8px 8px 0 0'
        }}
      >
        <Typography variant="h6" component="div">
          {getTitle()}
        </Typography>
        <div
          style={{
            cursor: 'pointer',
            padding: '4px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onClick={handleClose}
        >
          <Close size={20} />
        </div>
      </Box>

      {/* 内容区域 */}
      <DialogContent
        sx={{
          flex: 1,
          p: 0,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <TextField
          inputRef={textInputRef}
          multiline
          fullWidth
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder={
            artifact.type === 'Mermaid' 
              ? 'graph TD\n    A[Start] --> B[Process]\n    B --> C[End]'
              : artifact.type === 'SVG'
              ? '<svg width="100" height="100">\n  <circle cx="50" cy="50" r="40" fill="blue" />\n</svg>'
              : 'Enter your code here...'
          }
          sx={{
            flex: 1,
            '& .MuiInputBase-root': {
              height: '100%',
              alignItems: 'flex-start'
            },
            '& .MuiInputBase-input': {
              height: '100% !important',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: '14px',
              lineHeight: '1.5',
              resize: 'none'
            },
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                border: 'none'
              }
            }
          }}
        />
      </DialogContent>

      {/* 按钮区域 */}
      <DialogActions
        sx={{
          p: 2,
          borderTop: '1px solid #e0e0e0',
          bgcolor: '#f9f9f9',
          borderRadius: '0 0 8px 8px'
        }}
      >
        <Button 
          onClick={handleClose}
          variant="outlined"
          sx={{ mr: 1 }}
        >
          {intl.formatMessage({ id: 'cancel' }, { defaultMessage: 'Cancel' })}
        </Button>
        <Button 
          onClick={handleSave}
          variant="contained"
          color="primary"
        >
          {intl.formatMessage({ id: 'save' }, { defaultMessage: 'Save' })} (Ctrl+Enter)
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ArtifactEditModal;
